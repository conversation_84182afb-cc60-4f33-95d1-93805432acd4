<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 医疗专家诊断</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            height: 100vh;
            overflow: hidden;
        }
        
        .status-bar {
            height: 44px;
            background: rgba(0,0,0,0.9);
            color: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .container-fluid {
            height: calc(100vh - 44px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .register-card {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px 30px;
            width: 100%;
            max-width: 350px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            box-shadow: 0 10px 25px rgba(39, 174, 96, 0.3);
        }
        
        .logo-icon i {
            font-size: 35px;
            color: white;
        }
        
        .app-title {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .app-subtitle {
            font-size: 14px;
            color: #7f8c8d;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-control {
            border: 2px solid #ecf0f1;
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.9);
        }
        
        .form-control:focus {
            border-color: #27ae60;
            box-shadow: 0 0 0 0.2rem rgba(39, 174, 96, 0.25);
            background: white;
        }
        
        .input-group {
            position: relative;
        }
        
        .input-group-text {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #27ae60;
            z-index: 10;
            font-size: 18px;
        }
        
        .form-control.with-icon {
            padding-left: 50px;
        }
        
        .btn-register {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            border: none;
            border-radius: 15px;
            padding: 15px;
            font-size: 18px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(39, 174, 96, 0.3);
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(39, 174, 96, 0.4);
        }
        
        .login-link {
            text-align: center;
            margin-top: 20px;
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .login-link a {
            color: #27ae60;
            text-decoration: none;
            font-weight: 600;
        }
        
        .terms-text {
            font-size: 12px;
            color: #95a5a6;
            text-align: center;
            margin-top: 15px;
            line-height: 1.4;
        }
        
        .terms-text a {
            color: #27ae60;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- iOS状态栏 -->
    <div class="status-bar">
        <div>9:41</div>
        <div>
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi ms-1"></i>
            <i class="fas fa-battery-three-quarters ms-1"></i>
        </div>
    </div>
    
    <div class="container-fluid">
        <div class="register-card">
            <div class="logo-section">
                <div class="logo-icon">
                    <i class="fas fa-user-md"></i>
                </div>
                <div class="app-title">医疗专家</div>
                <div class="app-subtitle">专业诊断 · 贴心服务</div>
            </div>
            
            <form>
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text" class="form-control with-icon" placeholder="请输入姓名" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-mobile-alt"></i>
                        </span>
                        <input type="tel" class="form-control with-icon" placeholder="请输入手机号码" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" class="form-control with-icon" placeholder="请设置密码" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-shield-alt"></i>
                        </span>
                        <input type="password" class="form-control with-icon" placeholder="请确认密码" required>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-register">
                    <i class="fas fa-user-plus me-2"></i>立即注册
                </button>
                
                <div class="terms-text">
                    注册即表示同意 <a href="#">《用户协议》</a> 和 <a href="#">《隐私政策》</a>
                </div>
                
                <div class="login-link">
                    已有账号？<a href="login.html">立即登录</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
