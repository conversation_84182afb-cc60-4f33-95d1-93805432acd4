<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专家详情 - 医疗专家诊断</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            height: 100vh;
            overflow-x: hidden;
        }
        
        .status-bar {
            height: 44px;
            background: rgba(0,0,0,0.9);
            color: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .header {
            background: white;
            padding: 15px 20px;
            border-bottom: 1px solid #ecf0f1;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .back-btn {
            background: none;
            border: none;
            font-size: 20px;
            color: #27ae60;
            margin-right: 15px;
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .share-btn {
            background: none;
            border: none;
            font-size: 18px;
            color: #27ae60;
        }
        
        .content {
            height: calc(100vh - 44px - 60px - 80px);
            overflow-y: auto;
            padding-bottom: 20px;
        }
        
        .expert-profile {
            background: white;
            padding: 25px 20px;
            border-bottom: 8px solid #f8f9fa;
        }
        
        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .expert-avatar {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            margin-right: 20px;
            position: relative;
        }
        
        .online-indicator {
            position: absolute;
            bottom: 5px;
            right: 5px;
            width: 20px;
            height: 20px;
            background: #2ecc71;
            border: 3px solid white;
            border-radius: 50%;
        }
        
        .expert-info h2 {
            font-size: 22px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .expert-title {
            font-size: 16px;
            color: #27ae60;
            margin-bottom: 5px;
            font-weight: 600;
        }
        
        .expert-hospital {
            font-size: 14px;
            color: #7f8c8d;
        }
        
        .expert-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 12px;
        }
        
        .stat-number {
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
            display: block;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .rating-section {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 15px;
            background: rgba(39, 174, 96, 0.1);
            border-radius: 12px;
        }
        
        .rating-stars {
            color: #f39c12;
            font-size: 18px;
        }
        
        .rating-text {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .section {
            background: white;
            margin-bottom: 8px;
            padding: 20px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: 10px;
            color: #27ae60;
        }
        
        .specialties {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .specialty-tag {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .intro-text {
            line-height: 1.6;
            color: #555;
            font-size: 14px;
        }
        
        .review-item {
            padding: 15px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .review-item:last-child {
            border-bottom: none;
        }
        
        .review-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .reviewer-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }
        
        .review-date {
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .review-rating {
            color: #f39c12;
            margin-bottom: 8px;
        }
        
        .review-text {
            font-size: 14px;
            color: #555;
            line-height: 1.5;
        }
        
        .consultation-options {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #ecf0f1;
            padding: 15px 20px;
            box-shadow: 0 -5px 15px rgba(0,0,0,0.1);
        }
        
        .options-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .option-btn {
            padding: 15px;
            border-radius: 12px;
            border: none;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }
        
        .btn-text-consult {
            background: rgba(39, 174, 96, 0.1);
            color: #27ae60;
        }
        
        .btn-video-consult {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
        }
        
        .option-price {
            font-size: 18px;
            font-weight: 700;
        }
        
        .option-label {
            font-size: 12px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <!-- iOS状态栏 -->
    <div class="status-bar">
        <div>9:41</div>
        <div>
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi ms-1"></i>
            <i class="fas fa-battery-three-quarters ms-1"></i>
        </div>
    </div>
    
    <!-- 头部 -->
    <div class="header">
        <div class="header-left">
            <button class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1 class="header-title">专家详情</h1>
        </div>
        <button class="share-btn">
            <i class="fas fa-share-alt"></i>
        </button>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
        <!-- 专家资料 -->
        <div class="expert-profile">
            <div class="profile-header">
                <div class="expert-avatar">
                    <i class="fas fa-user-md"></i>
                    <div class="online-indicator"></div>
                </div>
                <div class="expert-info">
                    <h2>李明医生</h2>
                    <div class="expert-title">内科主任医师</div>
                    <div class="expert-hospital">北京协和医院</div>
                </div>
            </div>
            
            <div class="expert-stats">
                <div class="stat-item">
                    <span class="stat-number">15年</span>
                    <span class="stat-label">从业经验</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">1,280</span>
                    <span class="stat-label">咨询次数</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">98%</span>
                    <span class="stat-label">好评率</span>
                </div>
            </div>
            
            <div class="rating-section">
                <div class="rating-stars">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                </div>
                <div class="rating-text">4.9分 (128条评价)</div>
            </div>
        </div>
        
        <!-- 擅长领域 -->
        <div class="section">
            <h3 class="section-title">
                <i class="fas fa-star"></i>
                擅长领域
            </h3>
            <div class="specialties">
                <span class="specialty-tag">高血压诊治</span>
                <span class="specialty-tag">糖尿病管理</span>
                <span class="specialty-tag">心血管疾病</span>
                <span class="specialty-tag">内分泌疾病</span>
                <span class="specialty-tag">慢性病管理</span>
            </div>
        </div>
        
        <!-- 医生简介 -->
        <div class="section">
            <h3 class="section-title">
                <i class="fas fa-user"></i>
                医生简介
            </h3>
            <div class="intro-text">
                李明医生，北京协和医院内科主任医师，从事内科临床工作15年，具有丰富的临床经验。擅长高血压、糖尿病、心血管疾病等慢性病的诊断和治疗，对内分泌疾病有深入研究。曾在国外知名医院进修学习，发表学术论文30余篇。
                <br><br>
                秉承"以患者为中心"的理念，耐心细致地为每一位患者提供专业的医疗服务，深受患者信赖和好评。
            </div>
        </div>
        
        <!-- 患者评价 -->
        <div class="section">
            <h3 class="section-title">
                <i class="fas fa-comments"></i>
                患者评价
            </h3>
            
            <div class="review-item">
                <div class="review-header">
                    <div class="reviewer-name">张**</div>
                    <div class="review-date">2024-01-15</div>
                </div>
                <div class="review-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                </div>
                <div class="review-text">
                    李医生非常专业，耐心解答我的问题，给出的治疗建议很有效果，血压控制得很好。
                </div>
            </div>
            
            <div class="review-item">
                <div class="review-header">
                    <div class="reviewer-name">王**</div>
                    <div class="review-date">2024-01-12</div>
                </div>
                <div class="review-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                </div>
                <div class="review-text">
                    医生回复很及时，诊断准确，用药建议合理，服务态度很好。
                </div>
            </div>
            
            <div class="review-item">
                <div class="review-header">
                    <div class="reviewer-name">李**</div>
                    <div class="review-date">2024-01-10</div>
                </div>
                <div class="review-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="far fa-star"></i>
                </div>
                <div class="review-text">
                    专业水平很高，解释病情很详细，让我对自己的病情有了更清楚的认识。
                </div>
            </div>
        </div>
    </div>
    
    <!-- 咨询选项 -->
    <div class="consultation-options">
        <div class="options-grid">
            <button class="option-btn btn-text-consult">
                <div class="option-price">¥200</div>
                <div class="option-label">图文咨询</div>
            </button>
            <button class="option-btn btn-video-consult">
                <div class="option-price">¥500</div>
                <div class="option-label">视频咨询</div>
            </button>
        </div>
    </div>
</body>
</html>
