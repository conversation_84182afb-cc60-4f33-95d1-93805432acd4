<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 医疗专家诊断</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            height: 100vh;
            overflow: hidden;
        }
        
        .status-bar {
            height: 44px;
            background: rgba(0,0,0,0.9);
            color: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .container-fluid {
            height: calc(100vh - 44px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .login-card {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px 30px;
            width: 100%;
            max-width: 350px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .logo-section {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo-icon {
            width: 90px;
            height: 90px;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 15px 30px rgba(39, 174, 96, 0.3);
        }
        
        .logo-icon i {
            font-size: 40px;
            color: white;
        }
        
        .welcome-text {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        
        .welcome-subtitle {
            font-size: 16px;
            color: #7f8c8d;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-control {
            border: 2px solid #ecf0f1;
            border-radius: 15px;
            padding: 18px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.9);
        }
        
        .form-control:focus {
            border-color: #27ae60;
            box-shadow: 0 0 0 0.2rem rgba(39, 174, 96, 0.25);
            background: white;
        }
        
        .input-group {
            position: relative;
        }
        
        .input-group-text {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #27ae60;
            z-index: 10;
            font-size: 18px;
        }
        
        .form-control.with-icon {
            padding-left: 55px;
        }
        
        .forgot-password {
            text-align: right;
            margin-bottom: 25px;
        }
        
        .forgot-password a {
            color: #27ae60;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            border: none;
            border-radius: 15px;
            padding: 18px;
            font-size: 18px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(39, 174, 96, 0.3);
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(39, 174, 96, 0.4);
        }
        
        .divider {
            text-align: center;
            margin: 25px 0;
            position: relative;
            color: #bdc3c7;
            font-size: 14px;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #ecf0f1;
            z-index: 1;
        }
        
        .divider span {
            background: rgba(255,255,255,0.95);
            padding: 0 15px;
            position: relative;
            z-index: 2;
        }
        
        .social-login {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
        }
        
        .btn-social {
            flex: 1;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 12px;
            background: white;
            color: #7f8c8d;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .btn-social:hover {
            border-color: #27ae60;
            color: #27ae60;
        }
        
        .register-link {
            text-align: center;
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .register-link a {
            color: #27ae60;
            text-decoration: none;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- iOS状态栏 -->
    <div class="status-bar">
        <div>9:41</div>
        <div>
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi ms-1"></i>
            <i class="fas fa-battery-three-quarters ms-1"></i>
        </div>
    </div>
    
    <div class="container-fluid">
        <div class="login-card">
            <div class="logo-section">
                <div class="logo-icon">
                    <i class="fas fa-stethoscope"></i>
                </div>
                <div class="welcome-text">欢迎回来</div>
                <div class="welcome-subtitle">登录您的医疗专家账户</div>
            </div>
            
            <form>
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-mobile-alt"></i>
                        </span>
                        <input type="tel" class="form-control with-icon" placeholder="请输入手机号码" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" class="form-control with-icon" placeholder="请输入密码" required>
                    </div>
                </div>
                
                <div class="forgot-password">
                    <a href="#">忘记密码？</a>
                </div>
                
                <button type="submit" class="btn btn-login">
                    <i class="fas fa-sign-in-alt me-2"></i>立即登录
                </button>
                
                <div class="divider">
                    <span>或</span>
                </div>
                
                <div class="social-login">
                    <button type="button" class="btn btn-social">
                        <i class="fab fa-wechat"></i>
                    </button>
                    <button type="button" class="btn btn-social">
                        <i class="fab fa-apple"></i>
                    </button>
                    <button type="button" class="btn btn-social">
                        <i class="fas fa-fingerprint"></i>
                    </button>
                </div>
                
                <div class="register-link">
                    还没有账号？<a href="register.html">立即注册</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
