<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医疗专家诊断订阅App - 原型展示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 15px;
            min-height: 100vh;
        }
        
        .prototype-container {
            max-width: 1500px;
            margin: 0 auto;
        }
        
        .prototype-title {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .phone-frame {
            width: 280px;
            height: 600px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 35px;
            padding: 8px;
            margin: 10px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.4), 0 0 0 1px rgba(255,255,255,0.1);
            position: relative;
            transition: transform 0.3s ease;
        }
        
        .phone-frame:hover {
            transform: translateY(-5px);
        }
        
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 27px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 0 0 1px rgba(0,0,0,0.1);
        }
        
        .prototype-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 5px;
            margin-bottom: 40px;
        }
        
        .page-label {
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
            white-space: nowrap;
        }
        
        .description {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 25px;
            margin: 20px auto;
            max-width: 800px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(39, 174, 96, 0.2);
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list i {
            color: #27ae60;
            margin-right: 10px;
            width: 20px;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <h1 class="prototype-title">
            <i class="fas fa-mobile-alt text-success me-3"></i>
            医疗专家诊断订阅App
            <br><small class="text-muted fs-5">高保真原型展示</small>
        </h1>
        
        <div class="description">
            <h3 class="text-success mb-3"><i class="fas fa-stethoscope me-2"></i>产品概述</h3>
            <p class="mb-3">专为患者设计的医疗专家诊断订阅应用，解决患者难以自我诊断的痛点，提供专业的医疗咨询服务。</p>
            
            <div class="row">
                <div class="col-md-6">
                    <h5 class="text-success"><i class="fas fa-users me-2"></i>目标用户</h5>
                    <ul class="feature-list">
                        <li><i class="fas fa-user-injured"></i>需要医疗咨询的患者</li>
                        <li><i class="fas fa-question-circle"></i>症状描述困难的用户</li>
                        <li><i class="fas fa-clock"></i>需要快速诊断建议的人群</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5 class="text-success"><i class="fas fa-cogs me-2"></i>核心功能</h5>
                    <ul class="feature-list">
                        <li><i class="fas fa-user-plus"></i>用户注册登录</li>
                        <li><i class="fas fa-search"></i>专家浏览选择</li>
                        <li><i class="fas fa-comments"></i>在线医疗咨询</li>
                        <li><i class="fas fa-calendar-check"></i>订阅管理服务</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="prototype-grid">
            <!-- 注册页面 -->
            <div class="phone-frame">
                <div class="phone-screen">
                    <iframe src="register.html" width="100%" height="100%" frameborder="0"></iframe>
                </div>
                <div class="page-label"><i class="fas fa-user-plus me-1"></i>注册页面</div>
            </div>
            
            <!-- 登录页面 -->
            <div class="phone-frame">
                <div class="phone-screen">
                    <iframe src="login.html" width="100%" height="100%" frameborder="0"></iframe>
                </div>
                <div class="page-label"><i class="fas fa-sign-in-alt me-1"></i>登录页面</div>
            </div>
            
            <!-- 主页 -->
            <div class="phone-frame">
                <div class="phone-screen">
                    <iframe src="home.html" width="100%" height="100%" frameborder="0"></iframe>
                </div>
                <div class="page-label"><i class="fas fa-home me-1"></i>主页</div>
            </div>
            
            <!-- 专家列表页 -->
            <div class="phone-frame">
                <div class="phone-screen">
                    <iframe src="experts.html" width="100%" height="100%" frameborder="0"></iframe>
                </div>
                <div class="page-label"><i class="fas fa-user-md me-1"></i>专家列表</div>
            </div>
            
            <!-- 专家详情页 -->
            <div class="phone-frame">
                <div class="phone-screen">
                    <iframe src="expert-detail.html" width="100%" height="100%" frameborder="0"></iframe>
                </div>
                <div class="page-label"><i class="fas fa-info-circle me-1"></i>专家详情</div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
