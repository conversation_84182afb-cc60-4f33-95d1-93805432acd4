<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 医疗专家诊断</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            height: 100vh;
            overflow-x: hidden;
        }
        
        .status-bar {
            height: 44px;
            background: rgba(0,0,0,0.9);
            color: #fff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .header {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 20px;
            border-radius: 0 0 25px 25px;
        }
        
        .user-greeting {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .user-info h3 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .user-info p {
            font-size: 14px;
            opacity: 0.9;
            margin: 0;
        }
        
        .notification-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 18px;
            position: relative;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 18px;
            height: 18px;
            background: #e74c3c;
            border-radius: 50%;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .search-bar {
            background: rgba(255,255,255,0.15);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 12px 20px;
            color: white;
            backdrop-filter: blur(10px);
        }
        
        .search-bar::placeholder {
            color: rgba(255,255,255,0.8);
        }
        
        .main-content {
            padding: 20px;
            height: calc(100vh - 44px - 140px - 80px);
            overflow-y: auto;
        }
        
        .quick-actions {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .action-card {
            background: white;
            border-radius: 20px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: none;
            text-decoration: none;
            color: inherit;
        }
        
        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            color: inherit;
        }
        
        .action-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            color: white;
            font-size: 22px;
        }
        
        .action-title {
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .action-subtitle {
            font-size: 12px;
            color: #7f8c8d;
        }
        
        .featured-experts {
            margin-bottom: 25px;
        }
        
        .expert-card {
            background: white;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
        }
        
        .expert-card:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            color: inherit;
        }
        
        .expert-avatar {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-right: 15px;
        }
        
        .expert-info {
            flex: 1;
        }
        
        .expert-name {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 3px;
        }
        
        .expert-specialty {
            font-size: 13px;
            color: #27ae60;
            margin-bottom: 3px;
        }
        
        .expert-rating {
            font-size: 12px;
            color: #f39c12;
        }
        
        .expert-status {
            background: #2ecc71;
            color: white;
            padding: 5px 10px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #ecf0f1;
            padding: 10px 0;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -5px 15px rgba(0,0,0,0.1);
        }
        
        .nav-item {
            text-align: center;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
            padding: 5px;
        }
        
        .nav-item.active {
            color: #27ae60;
        }
        
        .nav-item i {
            font-size: 20px;
            margin-bottom: 3px;
            display: block;
        }
        
        .nav-item span {
            font-size: 11px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- iOS状态栏 -->
    <div class="status-bar">
        <div>9:41</div>
        <div>
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi ms-1"></i>
            <i class="fas fa-battery-three-quarters ms-1"></i>
        </div>
    </div>
    
    <!-- 头部 -->
    <div class="header">
        <div class="user-greeting">
            <div class="user-info">
                <h3>早上好，张先生</h3>
                <p>今天感觉怎么样？</p>
            </div>
            <button class="notification-btn">
                <i class="fas fa-bell"></i>
                <span class="notification-badge">3</span>
            </button>
        </div>
        <input type="text" class="form-control search-bar" placeholder="搜索症状、疾病或专家...">
    </div>
    
    <!-- 主要内容 -->
    <div class="main-content">
        <!-- 快速操作 -->
        <div class="quick-actions">
            <h4 class="section-title">快速服务</h4>
            <div class="action-grid">
                <a href="#" class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="action-title">在线咨询</div>
                    <div class="action-subtitle">即时问诊</div>
                </a>
                <a href="experts.html" class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <div class="action-title">找专家</div>
                    <div class="action-subtitle">专业诊断</div>
                </a>
                <a href="#" class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="action-title">预约挂号</div>
                    <div class="action-subtitle">线下就诊</div>
                </a>
                <a href="#" class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-file-medical"></i>
                    </div>
                    <div class="action-title">健康档案</div>
                    <div class="action-subtitle">记录管理</div>
                </a>
            </div>
        </div>
        
        <!-- 推荐专家 -->
        <div class="featured-experts">
            <h4 class="section-title">推荐专家</h4>
            <a href="expert-detail.html" class="expert-card">
                <div class="expert-avatar">
                    <i class="fas fa-user-md"></i>
                </div>
                <div class="expert-info">
                    <div class="expert-name">李明医生</div>
                    <div class="expert-specialty">内科主任医师</div>
                    <div class="expert-rating">
                        <i class="fas fa-star"></i> 4.9 (128评价)
                    </div>
                </div>
                <div class="expert-status">在线</div>
            </a>
            
            <a href="expert-detail.html" class="expert-card">
                <div class="expert-avatar">
                    <i class="fas fa-user-md"></i>
                </div>
                <div class="expert-info">
                    <div class="expert-name">王芳医生</div>
                    <div class="expert-specialty">皮肤科副主任</div>
                    <div class="expert-rating">
                        <i class="fas fa-star"></i> 4.8 (95评价)
                    </div>
                </div>
                <div class="expert-status">在线</div>
            </a>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <div class="bottom-nav">
        <a href="#" class="nav-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="experts.html" class="nav-item">
            <i class="fas fa-user-md"></i>
            <span>专家</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-comments"></i>
            <span>咨询</span>
        </a>
        <a href="#" class="nav-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>
</body>
</html>
